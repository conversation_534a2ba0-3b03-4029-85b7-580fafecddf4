import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/shutdown-settings',
    name: 'ShutdownSettings',
    component: () => import('../views/settings/ShutdownSettings.vue')
  },
  {
    path: '/knowledge-base',
    name: 'KnowledgeBase',
    component: () => import('../views/KnowledgeBase.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
