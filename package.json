{"name": "nezha", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build && electron-builder", "preview": "vite preview", "electron:dev": "vite", "electron:build": "vue-tsc -b && vite build && electron-builder", "electron:preview": "electron-builder --dir && electron --no-sandbox dist-electron/main", "format": "prettier --write \"src/**/*.{js,ts,vue,css}\" \"electron/**/*.{js,ts}\"", "format:check": "prettier --check \"src/**/*.{js,ts,vue,css}\" \"electron/**/*.{js,ts}\""}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "element-plus": "^2.10.4", "vue": "^3.5.17"}, "devDependencies": {"@types/node": "^24.0.15", "@unocss/preset-attributify": "^66.3.3", "@unocss/preset-icons": "^66.3.3", "@unocss/preset-uno": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/tsconfig": "^0.7.0", "electron": "^20.3.12", "electron-builder": "^24.9.1", "prettier": "^3.6.2", "typescript": "~5.8.3", "unocss": "^66.3.3", "vite": "^7.0.4", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vue-router": "^4.5.1", "vue-tsc": "^2.2.12"}, "main": "dist-electron/main.js"}