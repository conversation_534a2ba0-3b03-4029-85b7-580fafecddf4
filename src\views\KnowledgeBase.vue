<template>
  <div class="p-4">
    <el-card>
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-bold">知识库管理</h2>
        <el-button type="primary" @click="openAddDialog">新增知识</el-button>
      </div>
      <el-table :data="knowledgeBase" style="width: 100%">
        <el-table-column prop="author" label="作者" width="120" />
        <el-table-column prop="source" label="出处" width="180" />
        <el-table-column prop="content" label="内容" />
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <el-button size="small" @click="editKnowledge(scope.$index)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteKnowledge(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog v-model="dialogVisible" :title="editIndex === null ? '新增知识' : '编辑知识'">
      <el-form :model="form">
        <el-form-item label="作者">
          <el-input v-model="form.author" />
        </el-form-item>
        <el-form-item label="出处">
          <el-input v-model="form.source" />
        </el-form-item>
        <el-form-item label="内容">
          <el-input type="textarea" v-model="form.content" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveKnowledge">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
const { ipcRenderer } = window.require('electron')

const knowledgeBase = ref<any[]>([])
const dialogVisible = ref(false)
const editIndex = ref<number|null>(null)
const form = ref({ author: '', source: '', content: '' })

function loadKnowledgeBase() {
  ipcRenderer.invoke('get-config').then((config: any) => {
    knowledgeBase.value = config.knowledgeBase || []
  })
}

function openAddDialog() {
  editIndex.value = null
  form.value = { author: '', source: '', content: '' }
  dialogVisible.value = true
}

function editKnowledge(index: number) {
  editIndex.value = index
  form.value = { ...knowledgeBase.value[index] }
  dialogVisible.value = true
}

function saveKnowledge() {
  if (!form.value.author || !form.value.content) {
    ElMessage.error('作者和内容不能为空')
    return
  }
  if (editIndex.value === null) {
    knowledgeBase.value.push({ ...form.value })
  } else {
    knowledgeBase.value[editIndex.value] = { ...form.value }
  }
  updateConfig()
  dialogVisible.value = false
}

function deleteKnowledge(index: number) {
  knowledgeBase.value.splice(index, 1)
  updateConfig()
}

function updateConfig() {
  ipcRenderer.invoke('get-config').then((config: any) => {
    config.knowledgeBase = knowledgeBase.value
    ipcRenderer.invoke('save-config', config).then(() => {
      ElMessage.success('保存成功')
    })
  })
}

onMounted(loadKnowledgeBase)
</script>

<style scoped>
.p-4 { padding: 1rem; }
</style>
